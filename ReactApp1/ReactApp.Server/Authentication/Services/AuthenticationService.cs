using Microsoft.Graph;
using Microsoft.Identity.Web;
using ReactApp1.Server.Authentication.Interfaces;
using AuthenticationResult = ReactApp1.Server.Authentication.Interfaces.AuthenticationResult;
using UserInfo = ReactApp1.Server.Authentication.Interfaces.UserInfo;

namespace ReactApp1.Server.Authentication.Services
{
    public class AuthenticationService : IAuthenticationService
    {
        private readonly ITokenAcquisition _tokenAcquisition;
        private readonly GraphServiceClient _graphClient;
        
        private readonly ITokenValidator _tokenValidator;
        private readonly IConfiguration _configuration;

        public AuthenticationService(
            ITokenAcquisition tokenAcquisition,
            GraphServiceClient graphClient,
            ITokenValidator tokenValidator,
            IConfiguration configuration)
        {
            _tokenAcquisition = tokenAcquisition;
            _graphClient = graphClient;;
            _tokenValidator = tokenValidator;
            _configuration = configuration;
        }

        // public async Task<AuthenticationResult> ValidateTokenAsync(string token)
        // {
        //     if (string.IsNullOrEmpty(token))
        //     {
        //         return new AuthenticationResult { IsAuthenticated = false, ErrorMessage = "Token is empty" };
        //     }
        //
        //     try
        //     {
        //         bool isValid = await _tokenValidator.ValidateTokenAsync(token);
        //         
        //         if (!isValid)
        //         {
        //             return new AuthenticationResult { IsAuthenticated = false, ErrorMessage = "Invalid token" };
        //         }
        //
        //         var claims = await _tokenValidator.GetClaimsFromTokenAsync(token);
        //         string userId = claims.ContainsKey("oid") ? claims["oid"] : string.Empty;
        //         string userName = claims.ContainsKey("name") ? claims["name"] : string.Empty;
        //
        //         return new AuthenticationResult
        //         {
        //             IsAuthenticated = true,
        //             UserId = userId,
        //             UserName = userName,
        //             Token = token,
        //             Claims = claims
        //         };
        //     }
        //     catch (Exception ex)
        //     {
        //         return new AuthenticationResult { IsAuthenticated = false, ErrorMessage = ex.Message };
        //     }
        // }

        public async Task<UserInfo> GetUserInfoAsync(string userId)
        {
            try
            {
                var user = await _graphClient.Me.Request().GetAsync();
                return new UserInfo(user.Id, user.Mail ?? user.UserPrincipalName, user.DisplayName);
            }
            catch
            {
                return new UserInfo(userId, "unknown", "Unknown User");
            }
        }

        public Task<AuthenticationResult> ValidateTokenAsync(string token)
        {
            // Token validation is handled by the framework
            // This is a placeholder for additional validation logic
            return Task.FromResult(new AuthenticationResult(true, "userId", "<EMAIL>"));
        }

        public string GetLoginUrl(string redirectUri)
        {
            var tenantId = _configuration["AzureAd:TenantId"];
            var clientId = _configuration["AzureAd:ClientId"];
            var instance = _configuration["AzureAd:Instance"];
            
            var callbackUrl = $"https://localhost:5173/api/auth/callback";
            
            return $"{instance}{tenantId}/oauth2/v2.0/authorize?client_id={clientId}&response_type=code&redirect_uri={(redirectUri)}&response_mode=code&scope=openid%20profile%20email%20offline_access&state=url=${(callbackUrl)}";
        }

        public async Task<string> GetAccessTokenForUserAsync(string userId, string[] scopes)
        {
            try
            {
                return await _tokenAcquisition.GetAccessTokenForUserAsync(scopes);
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }
    }
}