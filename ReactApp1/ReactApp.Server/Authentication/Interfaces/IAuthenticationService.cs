namespace ReactApp1.Server.Authentication.Interfaces
{
    public interface IAuthenticationService
    {
        Task<AuthenticationResult> ValidateTokenAsync(string token);
        Task<UserInfo> GetUserInfoAsync(string userId);
        string GetLoginUrl(string redirectUri);
        
        Task<string> GetAccessTokenForUserAsync(string userId, string[] scopes);
    }
    
    public record AuthenticationResult(bool IsValid, string UserId, string Email);
    public record UserInfo(string Id, string Email, string DisplayName);
}
