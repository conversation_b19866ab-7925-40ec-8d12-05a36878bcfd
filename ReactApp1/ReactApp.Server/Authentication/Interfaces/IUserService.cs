namespace ReactApp1.Server.Authentication.Interfaces
{
    public interface IUserService
    {
        Task<UserProfile> GetUserInfoAsync(string userId);
        Task<UserProfile> GetUserProfileAsync(string userId);
        Task<bool> IsUserAuthorizedAsync(string userId, string resource);
        Task<bool> IsUserAuthenticatedAsync(string token);
    }
    
    public record UserProfile(string Id, string Email, string DisplayName, DateTime LastLogin);
}