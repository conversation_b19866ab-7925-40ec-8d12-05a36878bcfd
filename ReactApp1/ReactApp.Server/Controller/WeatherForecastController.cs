using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace ReactApp.Server.Controllers
{
    [ApiController]
    [Route("[controller]")]
    // [Authorize]  // Add this attribute to require authentication
    public class WeatherForecastController : ControllerBase
    {
        private static readonly string[] Summaries = new[]
        {
            "Freezing", "Bracing", "Chi<PERSON>", "Cool", "Mild", "Warm", "<PERSON><PERSON><PERSON>", "<PERSON>", "Sweltering", "Scorching"
        };

        private readonly ILogger<WeatherForecastController> _logger;

        public WeatherForecastController(ILogger<WeatherForecastController> logger)
        {
            _logger = logger;
        }

        [HttpGet(Name = "GetWeatherForecast")]
        public IEnumerable<ReactApp1.Server.Authentication.Services.WeatherForecast> Get()
        {
            return Enumerable.Range(1, 5).Select(index => new ReactApp1.Server.Authentication.Services.WeatherForecast
            {
                Date = DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
                TemperatureC = Random.Shared.Next(-20, 55),
                Summary = Summaries[Random.Shared.Next(Summaries.Length)]
            })
            .ToArray();
        }
    }
}
