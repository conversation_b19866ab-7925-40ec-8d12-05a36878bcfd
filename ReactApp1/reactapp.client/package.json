{"name": "reactapp1.client", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "overrides": {"@azure/msal-react": {"react": "$react"}, "@azure/msal-browser": {"react": "$react"}}, "dependencies": {"@azure/msal-browser": "^4.13.1", "@azure/msal-react": "^3.0.12", "@types/react-router-dom": "^5.3.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.3"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.4.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "typescript": "~5.3.3", "vite": "^5.1.4"}}