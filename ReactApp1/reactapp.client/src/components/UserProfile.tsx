import React, { useEffect, useState } from 'react';
import { useApiService } from '../hooks/useApiService';
import LoadingSpinner from './LoadingSpinner';
import ErrorMessage from './ErrorMessage';
import {useAuth} from "../contexts/AuthContext.tsx";

interface UserProfileData {
    id: string;
    email: string;
    displayName: string;
    jobTitle?: string;
    department?: string;
    officeLocation?: string;
    mobilePhone?: string;
    preferredLanguage?: string;
}

const UserProfile: React.FC = () => {
    const { user} = useAuth();
    const apiService = useApiService();
    const [profile, setProfile] = useState<UserProfileData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Helper function to safely get token claims
    const getTokenClaim = (claimName: string): string => {
        try {
            const claims = user?.idTokenClaims as Record<string, unknown>;
            return typeof claims?.[claimName] === 'string' ? (claims[claimName] as string) : 'N/A';
        } catch {
            return 'N/A';
        }
    };

    // Helper function to get token type with fallbacks
    const getTokenType = (): string => {
        const possibleClaims = ['token_use', 'typ', 'token_type'];
        for (const claim of possibleClaims) {
            const value = getTokenClaim(claim);
            if (value !== 'N/A') return value;
        }
        return 'N/A';
    };

    useEffect(() => {
        loadUserProfile();
    }, []);

    const loadUserProfile = async () => {
        setLoading(true);
        setError(null);

        try {
            const data = await apiService.getUserProfile();
            setProfile(data);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load profile');
            console.error('Error loading profile:', err);
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return <LoadingSpinner message="Loading profile..." />;
    }

    if (error) {
        return <ErrorMessage message={error} onRetry={loadUserProfile} />;
    }

    return (
        <div className="user-profile">
            <h2>User Profile</h2>

            <div className="profile-section">
                <h3>Account Information</h3>
                <div className="profile-grid">
                    <div className="profile-field">
                        <label>Username:</label>
                        <span>{user?.username || 'N/A'}</span>
                    </div>
                    <div className="profile-field">
                        <label>Account ID:</label>
                        <span>{user?.localAccountId || 'N/A'}</span>
                    </div>
                </div>
            </div>

            {profile && (
                <div className="profile-section">
                    <h3>Personal Information</h3>
                    <div className="profile-grid">
                        <div className="profile-field">
                            <label>Display Name:</label>
                            <span>{profile.displayName || 'N/A'}</span>
                        </div>
                        <div className="profile-field">
                            <label>Email:</label>
                            <span>{profile.email || 'N/A'}</span>
                        </div>
                        <div className="profile-field">
                            <label>Job Title:</label>
                            <span>{profile.jobTitle || 'N/A'}</span>
                        </div>
                        <div className="profile-field">
                            <label>Department:</label>
                            <span>{profile.department || 'N/A'}</span>
                        </div>
                        <div className="profile-field">
                            <label>Office Location:</label>
                            <span>{profile.officeLocation || 'N/A'}</span>
                        </div>
                        <div className="profile-field">
                            <label>Mobile Phone:</label>
                            <span>{profile.mobilePhone || 'N/A'}</span>
                        </div>
                        <div className="profile-field">
                            <label>Preferred Language:</label>
                            <span>{profile.preferredLanguage || 'N/A'}</span>
                        </div>
                    </div>
                </div>
            )}

            <div className="profile-section">
                <h3>Authentication Details</h3>
                <div className="profile-grid">
                    <div className="profile-field">
                        <label>Token Type:</label>
                        <span>{getTokenType()}</span>
                    </div>
                    <div className="profile-field">
                        <label>Tenant ID:</label>
                        <span>{user?.tenantId || 'N/A'}</span>
                    </div>
                    <div className="profile-field">
                        <label>Home Account ID:</label>
                        <span>{user?.homeAccountId || 'N/A'}</span>
                    </div>
                    <div className="profile-field">
                        <label>Environment:</label>
                        <span>{user?.environment || 'N/A'}</span>
                    </div>
                    <div className="profile-field">
                        <label>Authority Type:</label>
                        <span>{user?.authorityType || 'N/A'}</span>
                    </div>
                    <div className="profile-field" style={{ gridColumn: '1 / -1' }}>
                        <label>ID Token Claims:</label>
                        <pre style={{
                            background: '#f5f5f5',
                            padding: '10px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            overflow: 'auto',
                            maxHeight: '200px'
                        }}>
                            {user?.idTokenClaims ? JSON.stringify(user.idTokenClaims, null, 2) : 'N/A'}
                        </pre>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default UserProfile;