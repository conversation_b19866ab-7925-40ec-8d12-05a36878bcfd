import React, { useEffect, useState } from 'react';
import { useMsal } from '@azure/msal-react';
import { InteractionStatus } from '@azure/msal-browser';
import { useApiService } from '../hooks/useApiService';
import LoadingSpinner from './LoadingSpinner';
import ErrorMessage from './ErrorMessage';
import WeatherTable from './WeatherTable';
import UserInfo from './UserInfo';
import {Forecast} from "../interfaces/IApiService.ts";
import { loginRequest } from '../config/authConfig';

const WeatherDashboard: React.FC = () => {
    const { instance, accounts, inProgress } = useMsal();
    const isAuthenticated = accounts.length > 0;
    const loading = inProgress !== InteractionStatus.None;
    const account = accounts[0] || null;

    const handleLogin = async () => {
        try {
            await instance.loginPopup(loginRequest);
        } catch (error) {
            console.error('Lo<PERSON> failed:', error);
        }
    };

    const handleLogout = async () => {
        try {
            await instance.logoutPopup();
        } catch (error) {
            console.error('Logout failed:', error);
        }
    };
    const apiService = useApiService();
    const [forecasts, setForecasts] = useState<Forecast[]>([]);
    const [isLoadingData, setIsLoadingData] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (isAuthenticated) {
            loadWeatherData();
        } else {
            setForecasts([]);
            setError(null);
        }
    }, [isAuthenticated]);

    const loadWeatherData = async () => {
        setIsLoadingData(true);
        setError(null);

        try {
            const data = await apiService.getWeatherForecast();
            setForecasts(data);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load weather data');
            console.error('Error loading weather data:', err);
        } finally {
            setIsLoadingData(false);
        }
    };

    if (loading) {
        return <LoadingSpinner message="Checking authentication..." />;
    }

    if (!isAuthenticated) {
        return (
            <div className="auth-prompt">
                <p>You need to sign in to access weather forecasts.</p>
                <button onClick={login} className="btn btn-primary">
                    Sign In with Microsoft
                </button>
            </div>
        );
    }

    return (
        <div className="weather-dashboard">
            <UserInfo user={account} onLogout={logout} />

            {error && <ErrorMessage message={error} onRetry={loadWeatherData} />}

            {isLoadingData ? (
                <LoadingSpinner message="Loading weather data..." />
            ) : (
                <WeatherTable forecasts={forecasts} />
            )}
        </div>
    );
};

export default WeatherDashboard;