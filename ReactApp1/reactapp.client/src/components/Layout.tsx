import React from 'react';
import { Outlet, NavLink } from 'react-router-dom';
import { useMsal } from '@azure/msal-react';
import UserInfo from './UserInfo';

const Layout: React.FC = () => {
    const { instance, accounts } = useMsal();
    const isAuthenticated = accounts.length > 0;
    const account = accounts[0] || null;

    const handleLogout = async () => {
        try {
            await instance.logoutRedirect();
        } catch (error) {
            console.error('Logout failed:', error);
        }
    };

    return (
        <div className="app-layout">
            <header className="app-header">
                <div className="header-content">
                    {isAuthenticated && (
                        <nav className="main-nav">
                            <NavLink
                                to="/weather"
                                className={({ isActive }) =>
                                    isActive ? 'nav-link active' : 'nav-link'
                                }
                            >
                                Weather
                            </NavLink>
                            <NavLink
                                to="/profile"
                                className={({ isActive }) =>
                                    isActive ? 'nav-link active' : 'nav-link'
                                }
                            >
                                Profile
                            </NavLink>
                        </nav>
                    )}
                </div>
                {isAuthenticated && (
                    <div className="header-user">
                        <UserInfo user={account} onLogout={handleLogout} />
                    </div>
                )}
            </header>
            <main className="app-main">
                <Outlet />
            </main>
            <footer className="app-footer">
            </footer>
        </div>
    );
};

export default Layout;