import { useMsal } from '@azure/msal-react';
import { useMemo } from 'react';
import { ApiService } from '../services/ApiService';
import { apiConfig } from '../config/authConfig';
import { InteractionRequiredAuthError } from '@azure/msal-browser';

export const useApiService = () => {
    const { instance, accounts } = useMsal();

    const apiService = useMemo(() => {
        const getAccessToken = async (): Promise<string | null> => {
            if (!accounts[0]) return null;

            try {
                const response = await instance.acquireTokenSilent({
                    scopes: apiConfig.scopes,
                    account: accounts[0]
                });
                return response.accessToken;
            } catch (error) {
                if (error instanceof InteractionRequiredAuthError) {
                    try {
                        const response = await instance.acquireTokenPopup({
                            scopes: apiConfig.scopes,
                            account: accounts[0]
                        });
                        return response.accessToken;
                    } catch (popupError) {
                        console.error('Token acquisition failed:', popupError);
                        return null;
                    }
                }
                console.error('Token acquisition failed:', error);
                return null;
            }
        };

        return new ApiService(apiConfig.apiEndpoint, getAccessToken);
    }, [instance, accounts]);

    return apiService;
};
