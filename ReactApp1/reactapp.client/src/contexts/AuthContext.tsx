import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AccountInfo } from '@azure/msal-browser';
import { AuthService, IAuthService } from '../services/AuthService';
import {IApiService} from "../interfaces/IApiService.ts";
import {ApiService} from "../services/ApiService.ts";
import {apiConfig} from "../config/authConfig.ts";

interface AuthContextType {
    isAuthenticated: boolean;
    account: AccountInfo | null;
    user: AccountInfo | null; // Alias for account for backward compatibility
    login: () => Promise<void>;
    logout: () => Promise<void>;
    apiService: IApiService;
    loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

interface AuthProviderProps {
    children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
    const [authService] = useState<IAuthService>(() => new AuthService());
    const [apiService] = useState<IApiService>(() => {
        return new ApiService(apiConfig.apiEndpoint, () => authService.acquireTokenSilent());
    });
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [account, setAccount] = useState<AccountInfo | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const initAuth = async () => {
            await authService.initialize();
            const currentAccount = authService.getAccount();
            setAccount(currentAccount);
            setIsAuthenticated(!!currentAccount);
            setLoading(false);
        };

        initAuth();
    }, [authService]);

    const login = async () => {
        const response = await authService.login();
        if (response) {
            setAccount(response.account);
            setIsAuthenticated(true);
        }
    };

    const logout = async () => {
        await authService.logout();
        setAccount(null);
        setIsAuthenticated(false);
    };

    return (
        <AuthContext.Provider
            value={{
                isAuthenticated,
                account,
                user: account, // Alias for account for backward compatibility
                login,
                logout,
                apiService,
                loading,
            }}
        >
            {children}
        </AuthContext.Provider>
    );
};
