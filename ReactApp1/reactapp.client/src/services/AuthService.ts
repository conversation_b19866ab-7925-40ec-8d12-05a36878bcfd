import { PublicClientApplication, AccountInfo, AuthenticationResult } from "@azure/msal-browser";
import {loginRequest, msalConfig} from "../config/authConfig";

export interface IAuthService {
  initialize(): Promise<void>;
  login(): Promise<AuthenticationResult | null>;
  logout(): Promise<void>;
  getAccount(): AccountInfo | null;
  acquireTokenSilent(): Promise<string | null>;
}

export class AuthService implements IAuthService {
  private msalInstance: PublicClientApplication;

  constructor() {
    this.msalInstance = new PublicClientApplication(msalConfig);
  }

  async initialize(): Promise<void> {
    await this.msalInstance.initialize();
    await this.msalInstance.handleRedirectPromise();
  }

  async login(): Promise<AuthenticationResult | null> {
    try {
      const response = await this.msalInstance.loginPopup(loginRequest);
      return response;
    } catch (error) {
      console.error("Login failed:", error);
      return null;
    }
  }

  async logout(): Promise<void> {
    const account = this.getAccount();
    if (account) {
      await this.msalInstance.logoutPopup({
        account: account
      });
    }
  }

  getAccount(): AccountInfo | null {
    const accounts = this.msalInstance.getAllAccounts();
    return accounts.length > 0 ? accounts[0] : null;
  }

  async acquireTokenSilent(): Promise<string | null> {
    const account = this.getAccount();
    if (!account) return null;

    try {
      const response = await this.msalInstance.acquireTokenSilent({
        ...loginRequest,
        account: account
      });
      return response.accessToken;
    } catch (error) {
      console.error("Silent token acquisition failed:", error);
      // Fallback to interactive
      try {
        const response = await this.msalInstance.acquireTokenPopup(loginRequest);
        return response.accessToken;
      } catch (popupError) {
        console.error("Interactive token acquisition failed:", popupError);
        return null;
      }
    }
  }
}
