import { IApiService, Forecast } from '../interfaces/IApiService';

export class ApiService implements IApiService {
    private baseUrl: string;
    private getToken: () => Promise<string | null>;

    constructor(baseUrl: string, getToken: () => Promise<string | null>) {
        this.baseUrl = baseUrl;
        this.getToken = getToken;
    }

    private async fetchWithAuth(url: string, options: RequestInit = {}): Promise<Response> {
        const token = await this.getToken();
        if (!token) {
            throw new Error('No access token available');
        }

        return fetch(`${this.baseUrl}${url}`, {
            ...options,
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
                ...options.headers,
            },
        });
    }

    async getWeatherForecast(): Promise<Forecast[]> {
        const response = await this.fetchWithAuth('/weatherforecast');
        if (!response.ok) {
            throw new Error(`Failed to fetch weather data: ${response.statusText}`);
        }
        return response.json();
    }

    async getCurrentUser(): Promise<any> {
        const response = await this.fetchWithAuth('/auth/me');
        if (!response.ok) {
            throw new Error(`Failed to fetch user data: ${response.statusText}`);
        }
        return response.json();
    }

    async getUserProfile(): Promise<any> {
        const response = await this.fetchWithAuth('/user/profile');
        if (!response.ok) {
            throw new Error(`Failed to fetch profile: ${response.statusText}`);
        }
        return response.json();
    }
}